name: CI CPU testing

on: # https://help.github.com/en/actions/reference/events-that-trigger-workflows
  push:
    branches: [ master, develop ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ master, develop ]

jobs:
  cpu-tests:

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
        python-version: [ 3.8 ]
        model: [ 'yolov5s' ]  # models to test

    # Timeout: https://stackoverflow.com/a/59076067/4521646
    timeout-minutes: 50
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      # Note: This uses an internal pip API and may not always work
      # https://github.com/actions/cache/blob/master/examples.md#multiple-oss-in-a-workflow
      - name: Get pip cache
        id: pip-cache
        run: |
          python -c "from pip._internal.locations import USER_CACHE_DIR; print('::set-output name=dir::' + USER_CACHE_DIR)"

      - name: Cache pip
        uses: actions/cache@v1
        with:
          path: ${{ steps.pip-cache.outputs.dir }}
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.python-version }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -qr requirements.txt -f https://download.pytorch.org/whl/cpu/torch_stable.html
          pip install -q onnx
          python --version
          pip --version
          pip list
        shell: bash

      - name: Download data
        run: |
          # curl -L -o tmp.zip https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip
          # unzip -q tmp.zip -d ../
          # rm tmp.zip

      - name: Tests workflow
        run: |
          # export PYTHONPATH="$PWD"  # to run '$ python *.py' files in subdirectories
          di=cpu # inference devices  # define device

          # train
          python train.py --img 128 --batch 16 --weights ${{ matrix.model }}.pt --cfg ${{ matrix.model }}.yaml --epochs 1 --device $di
          # detect
          python detect.py --weights ${{ matrix.model }}.pt --device $di
          python detect.py --weights runs/train/exp/weights/last.pt --device $di
          # val
          python val.py --img 128 --batch 16 --weights ${{ matrix.model }}.pt --device $di
          python val.py --img 128 --batch 16 --weights runs/train/exp/weights/last.pt --device $di

          python hubconf.py  # hub
          python models/yolo.py --cfg ${{ matrix.model }}.yaml  # inspect
          python export.py --img 128 --batch 1 --weights ${{ matrix.model }}.pt  # export
        shell: bash
