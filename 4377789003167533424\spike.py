import requests
import cv2
def get_token():
    TOKEN_URL='https://aip.baidubce.com/oauth/2.0/token'
    headers={
        "Content-Type":"application/json"
    }
    SECRET_KEY="2Wg4RIfMPrDaULIMYPHaxu9UtDh1GfLb"
    API_KEY="QpjhSwuhvEq7ITEoINnsDhoj"
    params={
            "grant_type":"client_credentials",
            "client_id":API_KEY,
            "client_secret":SECRET_KEY
    }

    s=requests.session()
    s.keep_alive=False

    response=requests.post(url=TOKEN_URL,data=params,headers=headers)
    #print(response)

    #if(response):
    #       print(response.json())
    Token = response.json()['access_token']
    return Token
#print(Token)

def predict(img_path):
    Token = get_token()
    request_url="https://aip.baidubce.com/rpc/2.0/ai_custom/v1/detection/678905456778"
    request_url=request_url+"?access_token="+Token

    headers=headers={
        "Content-Type":"application/json"
    }

    import base64
    #img_path='/root/lwq/images/1.jpg'

    with open(img_path,'rb')as f:
        image_data = f.read()
        base64_data = base64.b64encode(image_data)
        s = base64_data.decode('UTF8')


    #base64_data

    import json
    params = {"image":s}
    params = json.dumps(params)

    #encoding:utf-8
    response = requests.post(request_url,data = params,headers = headers)

    #print(response)

    if response:
        print(response.json())

    data = response.json()
    print(data)

    import cv2
    import matplotlib.pyplot as plt

    img = cv2.imread(img_path)

    for i in range(len(data['results'])):
        x1 = data['results'][i]['location']['height']
        y1 = data['results'][i]['location']['left']
        x2 = data['results'][i]['location']['top']
        y2 = data['results'][i]['location']['width']

    font = cv2.FONT_HERSHEY_DUPLEX

    if data['results'][i]['name'] == 'person':
        cv2.rectangle(img,(y1,x2),(y1+y2,x2+x1),(0,0,255),2)
        cv2.putText(img,data['results'][i]['name'],(y1,x2),font,0.8,(0,0,255),1,)

    elif data['results'][i]['name'] == 'hat':
        cv2.rectangle(img,(y1,x2),(y1+y2,x2+x1),(255,0,0),2)
        cv2.putText(img,data['results'][i]['name'],(y1,x2),font,0.8,(255,0,0),1,)


    #cv2.imwrite(进行保存的图像名称，需要保存的图像对象)
    cv2.imwrite(img_path,img)

