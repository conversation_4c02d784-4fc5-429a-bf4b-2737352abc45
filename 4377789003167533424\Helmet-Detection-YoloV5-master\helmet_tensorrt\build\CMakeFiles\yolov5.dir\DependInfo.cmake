# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/tensorrtx/yolov5/calibrator.cpp" "/home/<USER>/tensorrtx/yolov5/build/CMakeFiles/yolov5.dir/calibrator.cpp.o"
  "/home/<USER>/tensorrtx/yolov5/yolov5.cpp" "/home/<USER>/tensorrtx/yolov5/build/CMakeFiles/yolov5.dir/yolov5.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "API_EXPORTS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "/usr/local/cuda/include"
  "/usr/include/x86_64-linux-gnu"
  "/usr/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/tensorrtx/yolov5/build/CMakeFiles/myplugins.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
