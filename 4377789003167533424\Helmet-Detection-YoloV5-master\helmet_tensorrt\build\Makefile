# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.14

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tensorrtx/yolov5

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tensorrtx/yolov5/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles /home/<USER>/tensorrtx/yolov5/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named myplugins

# Build rule for target.
myplugins: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 myplugins
.PHONY : myplugins

# fast build rule for target.
myplugins/fast:
	$(MAKE) -f CMakeFiles/myplugins.dir/build.make CMakeFiles/myplugins.dir/build
.PHONY : myplugins/fast

#=============================================================================
# Target rules for targets named yolov5

# Build rule for target.
yolov5: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 yolov5
.PHONY : yolov5

# fast build rule for target.
yolov5/fast:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/build
.PHONY : yolov5/fast

calibrator.o: calibrator.cpp.o

.PHONY : calibrator.o

# target to build an object file
calibrator.cpp.o:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/calibrator.cpp.o
.PHONY : calibrator.cpp.o

calibrator.i: calibrator.cpp.i

.PHONY : calibrator.i

# target to preprocess a source file
calibrator.cpp.i:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/calibrator.cpp.i
.PHONY : calibrator.cpp.i

calibrator.s: calibrator.cpp.s

.PHONY : calibrator.s

# target to generate assembly for a file
calibrator.cpp.s:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/calibrator.cpp.s
.PHONY : calibrator.cpp.s

yolov5.o: yolov5.cpp.o

.PHONY : yolov5.o

# target to build an object file
yolov5.cpp.o:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/yolov5.cpp.o
.PHONY : yolov5.cpp.o

yolov5.i: yolov5.cpp.i

.PHONY : yolov5.i

# target to preprocess a source file
yolov5.cpp.i:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/yolov5.cpp.i
.PHONY : yolov5.cpp.i

yolov5.s: yolov5.cpp.s

.PHONY : yolov5.s

# target to generate assembly for a file
yolov5.cpp.s:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/yolov5.cpp.s
.PHONY : yolov5.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... myplugins"
	@echo "... yolov5"
	@echo "... calibrator.o"
	@echo "... calibrator.i"
	@echo "... calibrator.s"
	@echo "... yolov5.o"
	@echo "... yolov5.i"
	@echo "... yolov5.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

