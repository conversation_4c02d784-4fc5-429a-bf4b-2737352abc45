# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.14

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

# The main recursive clean target
clean:

.PHONY : clean

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/tensorrtx/yolov5

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/tensorrtx/yolov5/build

#=============================================================================
# Target rules for target CMakeFiles/myplugins.dir

# All Build rule for target.
CMakeFiles/myplugins.dir/all:
	$(MAKE) -f CMakeFiles/myplugins.dir/build.make CMakeFiles/myplugins.dir/depend
	$(MAKE) -f CMakeFiles/myplugins.dir/build.make CMakeFiles/myplugins.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tensorrtx/yolov5/build/CMakeFiles --progress-num=1,2 "Built target myplugins"
.PHONY : CMakeFiles/myplugins.dir/all

# Include target in all.
all: CMakeFiles/myplugins.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
CMakeFiles/myplugins.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/myplugins.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles 0
.PHONY : CMakeFiles/myplugins.dir/rule

# Convenience name for target.
myplugins: CMakeFiles/myplugins.dir/rule

.PHONY : myplugins

# clean rule for target.
CMakeFiles/myplugins.dir/clean:
	$(MAKE) -f CMakeFiles/myplugins.dir/build.make CMakeFiles/myplugins.dir/clean
.PHONY : CMakeFiles/myplugins.dir/clean

# clean rule for target.
clean: CMakeFiles/myplugins.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/yolov5.dir

# All Build rule for target.
CMakeFiles/yolov5.dir/all: CMakeFiles/myplugins.dir/all
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/depend
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/tensorrtx/yolov5/build/CMakeFiles --progress-num=3,4,5 "Built target yolov5"
.PHONY : CMakeFiles/yolov5.dir/all

# Include target in all.
all: CMakeFiles/yolov5.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
CMakeFiles/yolov5.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles 5
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/yolov5.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/tensorrtx/yolov5/build/CMakeFiles 0
.PHONY : CMakeFiles/yolov5.dir/rule

# Convenience name for target.
yolov5: CMakeFiles/yolov5.dir/rule

.PHONY : yolov5

# clean rule for target.
CMakeFiles/yolov5.dir/clean:
	$(MAKE) -f CMakeFiles/yolov5.dir/build.make CMakeFiles/yolov5.dir/clean
.PHONY : CMakeFiles/yolov5.dir/clean

# clean rule for target.
clean: CMakeFiles/yolov5.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

