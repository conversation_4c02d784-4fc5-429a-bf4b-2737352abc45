# 所需权重文件清单

## 🎯 项目功能概述
这是一个基于Flask的深度学习应用部署系统，包含：
- 安全帽检测（使用百度AI API）
- 证件照处理（背景抠图和替换）
- 人脸识别（OpenCV Haar级联）
- 猫脸识别（OpenCV Haar级联）

## 📁 必需的权重文件

### 1. 证件照背景抠图模型
**文件名**: `model.onnx`
**位置**: 项目根目录 `4377789003167533424/model.onnx`
**用途**: 人像背景抠图，使用ONNX Runtime推理
**模型**: BriaRMBG (Background Removal Model)
**下载地址**:
- ✅ **正确链接**: https://huggingface.co/briaai/RMBG-1.4/resolve/main/onnx/model.onnx
- 🔄 **备用选择**:
  - FP16版本(更小): https://huggingface.co/briaai/RMBG-1.4/resolve/main/onnx/model_fp16.onnx (88MB)
  - 量化版本(最小): https://huggingface.co/briaai/RMBG-1.4/resolve/main/onnx/model_quantized.onnx (44MB)
- ❌ **旧链接已失效**: https://huggingface.co/briaai/RMBG-1.4/resolve/main/model.onnx

### 2. OpenCV Haar级联分类器文件
**文件名**: `haarcascade_frontalface_default.xml`
**位置**: 项目根目录 `4377789003167533424/haarcascade_frontalface_default.xml`
**用途**: 人脸检测
**下载地址**: 
- OpenCV官方: https://github.com/opencv/opencv/blob/master/data/haarcascades/haarcascade_frontalface_default.xml

**文件名**: `haarcascade_eye.xml`
**位置**: 项目根目录 `4377789003167533424/haarcascade_eye.xml`
**用途**: 眼睛检测
**下载地址**: 
- OpenCV官方: https://github.com/opencv/opencv/blob/master/data/haarcascades/haarcascade_eye.xml

**文件名**: `haarcascade_frontalcatface.xml`
**位置**: 项目根目录 `4377789003167533424/haarcascade_frontalcatface.xml`
**用途**: 猫脸检测
**下载地址**: 
- OpenCV官方: https://github.com/opencv/opencv/blob/master/data/haarcascades/haarcascade_frontalcatface.xml

## 🔑 百度AI平台配置详细指南

### 📋 需要开通的服务
根据代码分析，您需要在百度AI开放平台开通以下服务：

**服务名称**: **EasyDL零门槛AI开发平台 - 物体检测**
- **服务类型**: 自定义训练模型
- **具体功能**: 安全帽检测（人员+安全帽目标检测）
- **API接口**: `https://aip.baidubce.com/rpc/2.0/ai_custom/v1/detection/{模型ID}`

### 🚀 开通步骤详解

#### 第一步：注册百度AI开放平台
1. 访问：https://ai.baidu.com/
2. 点击右上角"登录"，使用百度账号登录
3. 完成实名认证（必需）

#### 第二步：进入EasyDL平台
1. 在百度AI首页点击"EasyDL零门槛AI开发平台"
2. 或直接访问：https://ai.baidu.com/easydl/
3. 选择"图像" → "物体检测"

#### 第三步：创建应用获取密钥
1. 进入EasyDL控制台：https://console.bce.baidu.com/ai/#/ai/easydlLiteImage/app/list
2. 点击"创建应用"
3. 填写应用名称（如：安全帽检测系统）
4. 创建成功后，在应用详情页获取：
   - **API Key** (替换代码中的API_KEY)
   - **Secret Key** (替换代码中的SECRET_KEY)

#### 第四步：训练安全帽检测模型
1. 创建数据集，上传包含人员和安全帽的图片
2. 标注数据：框选人员和安全帽区域
3. 训练模型（需要一定时间）
4. 模型训练完成后发布API服务
5. 获取模型ID（替换代码中的678905456778）

### 💰 费用说明
- **数据标注**: 免费额度内
- **模型训练**: 按算力时长计费
- **API调用**: 按调用次数计费，有免费额度
- **详细价格**: https://ai.baidu.com/ai-doc/EASYDL/Xl2tvkkx8

### 🔧 代码修改
替换 `spike.py` 中的以下内容：
```python
# 第8-9行：替换为您的密钥
SECRET_KEY = "您的Secret Key"
API_KEY = "您的API Key"

# 第30行：替换为您的模型ID
request_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/detection/您的模型ID"
```

## 📦 依赖包安装

```bash
pip install flask opencv-python onnxruntime pillow scikit-image numpy requests
```

## 🚀 快速开始指南

### 方案一：使用现有模型（推荐新手）
如果您只是想测试代码功能，可以：
1. 下载权重文件到项目根目录
2. 暂时使用代码中的示例API密钥（功能受限）
3. 安装依赖包：`pip install flask opencv-python onnxruntime pillow scikit-image numpy requests`
4. 运行：`python app.py`
5. 访问：`http://localhost:5000`

### 方案二：完整配置（推荐生产环境）
1. **下载权重文件**：下载所有必需的权重文件到项目根目录
2. **注册百度AI平台**：按照上述步骤获取自己的API密钥
3. **训练自定义模型**：上传安全帽数据集，训练专属模型
4. **修改代码配置**：替换API密钥和模型ID
5. **安装依赖包**：`pip install flask opencv-python onnxruntime pillow scikit-image numpy requests`
6. **运行系统**：`python app.py`
7. **访问界面**：`http://localhost:5000`

## ⚠️ 重要提醒

### 📥 模型文件下载
- **model.onnx** 文件较大（176MB），建议使用稳定网络下载
- **model_fp16.onnx** 半精度版本（88MB），速度与精度平衡
- **model_quantized.onnx** 量化版本（44MB），最小但可能精度略降
- **Haar级联文件** 相对较小（几KB），可以快速下载

### 🔑 API配置
- **百度AI API** 需要实名认证和可能的付费使用
- 需要替换代码中的API_KEY和SECRET_KEY为您自己的密钥

### 📂 文件位置
- 确保所有文件放在正确的位置，否则程序会报错
- 所有权重文件都应该放在项目根目录 `4377789003167533424/`

## 🔍 文件检查命令

在项目目录下运行以检查文件是否齐全：
```bash
# Windows PowerShell
Get-ChildItem -Name "model.onnx", "haarcascade_*.xml"

# Linux/Mac
ls -la model.onnx haarcascade_*.xml
```

应该看到4个文件：
- model.onnx
- haarcascade_frontalface_default.xml  
- haarcascade_eye.xml
- haarcascade_frontalcatface.xml
