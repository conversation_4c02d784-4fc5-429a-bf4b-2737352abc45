# 🚀 项目运行指南

## 📋 项目概述
这是一个基于Flask的深度学习应用系统，包含：
- 安全帽检测（百度AI API）
- 证件照处理（背景抠图和替换）
- 人脸识别（OpenCV）
- 猫脸识别（OpenCV）

## 🔧 环境准备

### 第一步：安装Python
1. **下载Python**：访问 https://www.python.org/downloads/
2. **选择版本**：推荐Python 3.8-3.11
3. **安装时注意**：勾选"Add Python to PATH"
4. **验证安装**：打开命令行输入 `python --version`

### 第二步：安装依赖包
在项目目录下运行：
```bash
pip install flask opencv-python onnxruntime pillow scikit-image numpy requests
```

### 第三步：下载权重文件
根据《所需权重文件清单.md》下载以下文件到项目根目录：

**必需文件**：
1. `model.onnx` - 证件照背景抠图模型
   - 下载地址：https://huggingface.co/briaai/RMBG-1.4/resolve/main/onnx/model.onnx
2. `haarcascade_frontalface_default.xml` - 人脸检测
3. `haarcascade_eye.xml` - 眼睛检测
4. `haarcascade_frontalcatface.xml` - 猫脸检测

## 🚀 运行步骤

### 方法一：直接运行（推荐）
```bash
# 1. 进入项目目录
cd 4377789003167533424

# 2. 运行Flask应用
python app.py
```

### 方法二：使用Python模块方式
```bash
python -m flask --app app run --host=0.0.0.0 --port=5000
```

## 🌐 访问系统

运行成功后，在浏览器中访问：
- **主页**：http://localhost:5000
- **或者**：http://127.0.0.1:5000

## 📱 功能使用

### 1. 安全帽检测
- 点击"目标检测"
- 上传包含人员的图片
- 系统会自动检测人员和安全帽

### 2. 证件照处理
- 点击"证件照更换"
- 上传人像照片
- 系统会自动抠图并替换蓝色背景

### 3. 人脸识别
- 点击"人脸识别"
- 上传包含人脸的图片
- 系统会检测并标注人脸和眼睛

### 4. 猫脸识别
- 点击"猫脸识别"
- 上传猫咪照片
- 系统会检测并标注猫脸

## ⚠️ 常见问题

### 问题1：Python未找到
**解决方案**：
- 重新安装Python并勾选"Add to PATH"
- 或使用完整路径：`C:\Python39\python.exe app.py`

### 问题2：模块未找到
**错误**：`ModuleNotFoundError: No module named 'flask'`
**解决方案**：
```bash
pip install flask opencv-python onnxruntime pillow scikit-image numpy requests
```

### 问题3：权重文件缺失
**错误**：`FileNotFoundError: model.onnx`
**解决方案**：
- 按照《所需权重文件清单.md》下载所有权重文件
- 确保文件放在项目根目录

### 问题4：百度API错误
**错误**：API调用失败
**解决方案**：
- 检查网络连接
- 申请自己的百度AI API密钥
- 按照《所需权重文件清单.md》配置API

### 问题5：端口被占用
**错误**：`Address already in use`
**解决方案**：
- 修改app.py最后一行的端口号：`app.run(host="0.0.0.0",port=5001)`
- 或关闭占用5000端口的程序

## 🔍 调试模式

如需调试，修改app.py最后一行：
```python
if __name__ == '__main__':
    app.run(host="0.0.0.0", port=5000, debug=True)
```

## 📞 技术支持

如遇到问题：
1. 检查《所需权重文件清单.md》
2. 确认所有依赖包已安装
3. 验证权重文件是否下载完整
4. 检查Python版本兼容性

## 🎯 快速测试

最简单的测试方法：
1. 确保Python已安装
2. 安装依赖：`pip install flask opencv-python onnxruntime pillow scikit-image numpy requests`
3. 下载model.onnx文件
4. 运行：`python app.py`
5. 访问：http://localhost:5000

**注意**：部分功能需要权重文件才能正常工作，建议按照完整步骤配置。
