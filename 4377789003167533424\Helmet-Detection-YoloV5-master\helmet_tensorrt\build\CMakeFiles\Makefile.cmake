# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.14

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.14.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.14.4/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.14.4/CMakeSystem.cmake"
  "CMakeFiles/feature_tests.c"
  "CMakeFiles/feature_tests.cxx"
  "CMakeFiles/myplugins.dir/myplugins_generated_yololayer.cu.o.cmake.pre-gen"
  "CMakeFiles/myplugins.dir/myplugins_generated_yololayer.cu.o.depend"
  "/usr/lib/aarch64-linux-gnu/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/opencv4/OpenCVModules.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.14/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.14/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.14/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.14/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.14/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.14/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-C-FeatureTests.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-CXX-FeatureTests.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/MIPSpro-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.14/Modules/FindCUDA.cmake"
  "/usr/share/cmake-3.14/Modules/FindCUDA/run_nvcc.cmake"
  "/usr/share/cmake-3.14/Modules/FindCUDA/select_compute_arch.cmake"
  "/usr/share/cmake-3.14/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.14/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.14/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake-3.14/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.14/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.14.4/CMakeSystem.cmake"
  "CMakeFiles/3.14.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.14.4/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.14.4/CMakeCCompiler.cmake"
  "CMakeFiles/3.14.4/CMakeCXXCompiler.cmake"
  "CMakeFiles/myplugins.dir/myplugins_generated_yololayer.cu.o.cmake.pre-gen"
  "CMakeFiles/myplugins.dir/myplugins_generated_yololayer.cu.o.Debug.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/myplugins.dir/DependInfo.cmake"
  "CMakeFiles/yolov5.dir/DependInfo.cmake"
  )
