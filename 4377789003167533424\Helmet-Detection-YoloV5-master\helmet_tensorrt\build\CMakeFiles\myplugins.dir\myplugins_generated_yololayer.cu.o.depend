# Generated by: make2cmake.cmake
SET(CUDA_NVCC_DEPEND
  "/home/<USER>/tensorrtx/yolov5/cuda_utils.h"
 "/home/<USER>/tensorrtx/yolov5/macros.h"
 "/home/<USER>/tensorrtx/yolov5/yololayer.cu"
 "/home/<USER>/tensorrtx/yolov5/yololayer.h"
 "/usr/include/aarch64-linux-gnu/NvInfer.h"
 "/usr/include/aarch64-linux-gnu/NvInferRuntime.h"
 "/usr/include/aarch64-linux-gnu/NvInferRuntimeCommon.h"
 "/usr/include/aarch64-linux-gnu/NvInferVersion.h"
 "/usr/include/aarch64-linux-gnu/asm/errno.h"
 "/usr/include/aarch64-linux-gnu/bits/_G_config.h"
 "/usr/include/aarch64-linux-gnu/bits/byteswap-16.h"
 "/usr/include/aarch64-linux-gnu/bits/byteswap.h"
 "/usr/include/aarch64-linux-gnu/bits/cpu-set.h"
 "/usr/include/aarch64-linux-gnu/bits/endian.h"
 "/usr/include/aarch64-linux-gnu/bits/errno.h"
 "/usr/include/aarch64-linux-gnu/bits/floatn-common.h"
 "/usr/include/aarch64-linux-gnu/bits/floatn.h"
 "/usr/include/aarch64-linux-gnu/bits/flt-eval-method.h"
 "/usr/include/aarch64-linux-gnu/bits/fp-fast.h"
 "/usr/include/aarch64-linux-gnu/bits/fp-logb.h"
 "/usr/include/aarch64-linux-gnu/bits/iscanonical.h"
 "/usr/include/aarch64-linux-gnu/bits/libc-header-start.h"
 "/usr/include/aarch64-linux-gnu/bits/libio.h"
 "/usr/include/aarch64-linux-gnu/bits/libm-simd-decl-stubs.h"
 "/usr/include/aarch64-linux-gnu/bits/local_lim.h"
 "/usr/include/aarch64-linux-gnu/bits/locale.h"
 "/usr/include/aarch64-linux-gnu/bits/long-double.h"
 "/usr/include/aarch64-linux-gnu/bits/math-finite.h"
 "/usr/include/aarch64-linux-gnu/bits/math-vector.h"
 "/usr/include/aarch64-linux-gnu/bits/mathcalls-helper-functions.h"
 "/usr/include/aarch64-linux-gnu/bits/mathcalls.h"
 "/usr/include/aarch64-linux-gnu/bits/mathinline.h"
 "/usr/include/aarch64-linux-gnu/bits/posix1_lim.h"
 "/usr/include/aarch64-linux-gnu/bits/posix2_lim.h"
 "/usr/include/aarch64-linux-gnu/bits/pthreadtypes-arch.h"
 "/usr/include/aarch64-linux-gnu/bits/pthreadtypes.h"
 "/usr/include/aarch64-linux-gnu/bits/sched.h"
 "/usr/include/aarch64-linux-gnu/bits/select.h"
 "/usr/include/aarch64-linux-gnu/bits/select2.h"
 "/usr/include/aarch64-linux-gnu/bits/setjmp.h"
 "/usr/include/aarch64-linux-gnu/bits/stdint-intn.h"
 "/usr/include/aarch64-linux-gnu/bits/stdint-uintn.h"
 "/usr/include/aarch64-linux-gnu/bits/stdio.h"
 "/usr/include/aarch64-linux-gnu/bits/stdio2.h"
 "/usr/include/aarch64-linux-gnu/bits/stdio_lim.h"
 "/usr/include/aarch64-linux-gnu/bits/stdlib-bsearch.h"
 "/usr/include/aarch64-linux-gnu/bits/stdlib-float.h"
 "/usr/include/aarch64-linux-gnu/bits/stdlib.h"
 "/usr/include/aarch64-linux-gnu/bits/string_fortified.h"
 "/usr/include/aarch64-linux-gnu/bits/strings_fortified.h"
 "/usr/include/aarch64-linux-gnu/bits/sys_errlist.h"
 "/usr/include/aarch64-linux-gnu/bits/sysmacros.h"
 "/usr/include/aarch64-linux-gnu/bits/thread-shared-types.h"
 "/usr/include/aarch64-linux-gnu/bits/time.h"
 "/usr/include/aarch64-linux-gnu/bits/timex.h"
 "/usr/include/aarch64-linux-gnu/bits/types.h"
 "/usr/include/aarch64-linux-gnu/bits/types/FILE.h"
 "/usr/include/aarch64-linux-gnu/bits/types/__FILE.h"
 "/usr/include/aarch64-linux-gnu/bits/types/__locale_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/__mbstate_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/__sigset_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/clock_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/clockid_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/locale_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/mbstate_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/sigset_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/struct_itimerspec.h"
 "/usr/include/aarch64-linux-gnu/bits/types/struct_timespec.h"
 "/usr/include/aarch64-linux-gnu/bits/types/struct_timeval.h"
 "/usr/include/aarch64-linux-gnu/bits/types/struct_tm.h"
 "/usr/include/aarch64-linux-gnu/bits/types/time_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/timer_t.h"
 "/usr/include/aarch64-linux-gnu/bits/types/wint_t.h"
 "/usr/include/aarch64-linux-gnu/bits/typesizes.h"
 "/usr/include/aarch64-linux-gnu/bits/uintn-identity.h"
 "/usr/include/aarch64-linux-gnu/bits/uio_lim.h"
 "/usr/include/aarch64-linux-gnu/bits/waitflags.h"
 "/usr/include/aarch64-linux-gnu/bits/waitstatus.h"
 "/usr/include/aarch64-linux-gnu/bits/wchar.h"
 "/usr/include/aarch64-linux-gnu/bits/wchar2.h"
 "/usr/include/aarch64-linux-gnu/bits/wctype-wchar.h"
 "/usr/include/aarch64-linux-gnu/bits/wordsize.h"
 "/usr/include/aarch64-linux-gnu/bits/xopen_lim.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/atomic_word.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/c++allocator.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/c++config.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/c++locale.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/cpu_defines.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/ctype_base.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/ctype_inline.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/error_constants.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/gthr-default.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/gthr.h"
 "/usr/include/aarch64-linux-gnu/c++/7/bits/os_defines.h"
 "/usr/include/aarch64-linux-gnu/gnu/stubs-lp64.h"
 "/usr/include/aarch64-linux-gnu/gnu/stubs.h"
 "/usr/include/aarch64-linux-gnu/sys/cdefs.h"
 "/usr/include/aarch64-linux-gnu/sys/select.h"
 "/usr/include/aarch64-linux-gnu/sys/sysmacros.h"
 "/usr/include/aarch64-linux-gnu/sys/types.h"
 "/usr/include/alloca.h"
 "/usr/include/asm-generic/errno-base.h"
 "/usr/include/asm-generic/errno.h"
 "/usr/include/assert.h"
 "/usr/include/c++/7/backward/binders.h"
 "/usr/include/c++/7/bits/alloc_traits.h"
 "/usr/include/c++/7/bits/allocator.h"
 "/usr/include/c++/7/bits/basic_ios.h"
 "/usr/include/c++/7/bits/basic_ios.tcc"
 "/usr/include/c++/7/bits/basic_string.h"
 "/usr/include/c++/7/bits/basic_string.tcc"
 "/usr/include/c++/7/bits/char_traits.h"
 "/usr/include/c++/7/bits/concept_check.h"
 "/usr/include/c++/7/bits/cpp_type_traits.h"
 "/usr/include/c++/7/bits/cxxabi_forced.h"
 "/usr/include/c++/7/bits/cxxabi_init_exception.h"
 "/usr/include/c++/7/bits/exception.h"
 "/usr/include/c++/7/bits/exception_defines.h"
 "/usr/include/c++/7/bits/exception_ptr.h"
 "/usr/include/c++/7/bits/functexcept.h"
 "/usr/include/c++/7/bits/functional_hash.h"
 "/usr/include/c++/7/bits/hash_bytes.h"
 "/usr/include/c++/7/bits/ios_base.h"
 "/usr/include/c++/7/bits/istream.tcc"
 "/usr/include/c++/7/bits/locale_classes.h"
 "/usr/include/c++/7/bits/locale_classes.tcc"
 "/usr/include/c++/7/bits/locale_facets.h"
 "/usr/include/c++/7/bits/locale_facets.tcc"
 "/usr/include/c++/7/bits/localefwd.h"
 "/usr/include/c++/7/bits/memoryfwd.h"
 "/usr/include/c++/7/bits/move.h"
 "/usr/include/c++/7/bits/nested_exception.h"
 "/usr/include/c++/7/bits/ostream.tcc"
 "/usr/include/c++/7/bits/ostream_insert.h"
 "/usr/include/c++/7/bits/postypes.h"
 "/usr/include/c++/7/bits/predefined_ops.h"
 "/usr/include/c++/7/bits/ptr_traits.h"
 "/usr/include/c++/7/bits/range_access.h"
 "/usr/include/c++/7/bits/std_abs.h"
 "/usr/include/c++/7/bits/stl_algobase.h"
 "/usr/include/c++/7/bits/stl_bvector.h"
 "/usr/include/c++/7/bits/stl_construct.h"
 "/usr/include/c++/7/bits/stl_function.h"
 "/usr/include/c++/7/bits/stl_iterator.h"
 "/usr/include/c++/7/bits/stl_iterator_base_funcs.h"
 "/usr/include/c++/7/bits/stl_iterator_base_types.h"
 "/usr/include/c++/7/bits/stl_pair.h"
 "/usr/include/c++/7/bits/stl_uninitialized.h"
 "/usr/include/c++/7/bits/stl_vector.h"
 "/usr/include/c++/7/bits/streambuf.tcc"
 "/usr/include/c++/7/bits/streambuf_iterator.h"
 "/usr/include/c++/7/bits/stringfwd.h"
 "/usr/include/c++/7/bits/vector.tcc"
 "/usr/include/c++/7/cctype"
 "/usr/include/c++/7/cerrno"
 "/usr/include/c++/7/clocale"
 "/usr/include/c++/7/cmath"
 "/usr/include/c++/7/cstddef"
 "/usr/include/c++/7/cstdint"
 "/usr/include/c++/7/cstdio"
 "/usr/include/c++/7/cstdlib"
 "/usr/include/c++/7/cwchar"
 "/usr/include/c++/7/cwctype"
 "/usr/include/c++/7/debug/assertions.h"
 "/usr/include/c++/7/debug/debug.h"
 "/usr/include/c++/7/exception"
 "/usr/include/c++/7/ext/alloc_traits.h"
 "/usr/include/c++/7/ext/atomicity.h"
 "/usr/include/c++/7/ext/new_allocator.h"
 "/usr/include/c++/7/ext/numeric_traits.h"
 "/usr/include/c++/7/ext/string_conversions.h"
 "/usr/include/c++/7/ext/type_traits.h"
 "/usr/include/c++/7/initializer_list"
 "/usr/include/c++/7/ios"
 "/usr/include/c++/7/iosfwd"
 "/usr/include/c++/7/iostream"
 "/usr/include/c++/7/istream"
 "/usr/include/c++/7/math.h"
 "/usr/include/c++/7/new"
 "/usr/include/c++/7/ostream"
 "/usr/include/c++/7/stdexcept"
 "/usr/include/c++/7/stdlib.h"
 "/usr/include/c++/7/streambuf"
 "/usr/include/c++/7/string"
 "/usr/include/c++/7/system_error"
 "/usr/include/c++/7/type_traits"
 "/usr/include/c++/7/typeinfo"
 "/usr/include/c++/7/vector"
 "/usr/include/ctype.h"
 "/usr/include/endian.h"
 "/usr/include/errno.h"
 "/usr/include/features.h"
 "/usr/include/limits.h"
 "/usr/include/linux/errno.h"
 "/usr/include/linux/limits.h"
 "/usr/include/locale.h"
 "/usr/include/math.h"
 "/usr/include/pthread.h"
 "/usr/include/sched.h"
 "/usr/include/stdc-predef.h"
 "/usr/include/stdint.h"
 "/usr/include/stdio.h"
 "/usr/include/stdlib.h"
 "/usr/include/string.h"
 "/usr/include/strings.h"
 "/usr/include/time.h"
 "/usr/include/wchar.h"
 "/usr/include/wctype.h"
 "/usr/lib/gcc/aarch64-linux-gnu/7/include-fixed/limits.h"
 "/usr/lib/gcc/aarch64-linux-gnu/7/include-fixed/syslimits.h"
 "/usr/lib/gcc/aarch64-linux-gnu/7/include/stdarg.h"
 "/usr/lib/gcc/aarch64-linux-gnu/7/include/stddef.h"
 "/usr/lib/gcc/aarch64-linux-gnu/7/include/stdint.h"
 "/usr/local/cuda/include/builtin_types.h"
 "/usr/local/cuda/include/channel_descriptor.h"
 "/usr/local/cuda/include/crt/common_functions.h"
 "/usr/local/cuda/include/crt/device_double_functions.h"
 "/usr/local/cuda/include/crt/device_double_functions.hpp"
 "/usr/local/cuda/include/crt/device_functions.h"
 "/usr/local/cuda/include/crt/device_functions.hpp"
 "/usr/local/cuda/include/crt/host_config.h"
 "/usr/local/cuda/include/crt/host_defines.h"
 "/usr/local/cuda/include/crt/math_functions.h"
 "/usr/local/cuda/include/crt/math_functions.hpp"
 "/usr/local/cuda/include/crt/sm_70_rt.h"
 "/usr/local/cuda/include/crt/sm_70_rt.hpp"
 "/usr/local/cuda/include/cuda_device_runtime_api.h"
 "/usr/local/cuda/include/cuda_runtime.h"
 "/usr/local/cuda/include/cuda_runtime_api.h"
 "/usr/local/cuda/include/cuda_surface_types.h"
 "/usr/local/cuda/include/cuda_texture_types.h"
 "/usr/local/cuda/include/device_atomic_functions.h"
 "/usr/local/cuda/include/device_atomic_functions.hpp"
 "/usr/local/cuda/include/device_launch_parameters.h"
 "/usr/local/cuda/include/device_types.h"
 "/usr/local/cuda/include/driver_functions.h"
 "/usr/local/cuda/include/driver_types.h"
 "/usr/local/cuda/include/library_types.h"
 "/usr/local/cuda/include/sm_20_atomic_functions.h"
 "/usr/local/cuda/include/sm_20_atomic_functions.hpp"
 "/usr/local/cuda/include/sm_20_intrinsics.h"
 "/usr/local/cuda/include/sm_20_intrinsics.hpp"
 "/usr/local/cuda/include/sm_30_intrinsics.h"
 "/usr/local/cuda/include/sm_30_intrinsics.hpp"
 "/usr/local/cuda/include/sm_32_atomic_functions.h"
 "/usr/local/cuda/include/sm_32_atomic_functions.hpp"
 "/usr/local/cuda/include/sm_32_intrinsics.h"
 "/usr/local/cuda/include/sm_32_intrinsics.hpp"
 "/usr/local/cuda/include/sm_35_atomic_functions.h"
 "/usr/local/cuda/include/sm_35_intrinsics.h"
 "/usr/local/cuda/include/sm_60_atomic_functions.h"
 "/usr/local/cuda/include/sm_60_atomic_functions.hpp"
 "/usr/local/cuda/include/sm_61_intrinsics.h"
 "/usr/local/cuda/include/sm_61_intrinsics.hpp"
 "/usr/local/cuda/include/surface_functions.h"
 "/usr/local/cuda/include/surface_indirect_functions.h"
 "/usr/local/cuda/include/surface_types.h"
 "/usr/local/cuda/include/texture_fetch_functions.h"
 "/usr/local/cuda/include/texture_indirect_functions.h"
 "/usr/local/cuda/include/texture_types.h"
 "/usr/local/cuda/include/vector_functions.h"
 "/usr/local/cuda/include/vector_functions.hpp"
 "/usr/local/cuda/include/vector_types.h"
)

