@echo off
echo Installing YOLOv5 dependencies...

pip install "torch>=1.7.0" --index-url https://download.pytorch.org/whl/cu118
pip install "torchvision>=0.8.0" --index-url https://download.pytorch.org/whl/cu118
pip install "Pillow"
pip install "numpy>=1.18.5"
pip install "matplotlib>=3.2.2"
pip install "opencv-python>=4.1.2"
pip install "PyYAML>=5.3.1"
pip install "scipy>=1.4.1"
pip install "tqdm>=4.41.0"

echo Installation completed!
pause
