from flask import Flask,request
from flask import render_template
import os
from spike import predict
from infer import predict_docu
from face import  predict_face
from cat import predict_cat
app = Flask(__name__)

@app.route("/")
def hello():
    return render_template('index.html')

def return_img_stream(img_local_path):
    '''
    发送本地图片
    '''
    import base64
    img_stream = ''
    with open(img_local_path,'rb') as img_f:
        img_stream = img_f.read()
        img_stream = base64.b64encode(img_stream).decode()
    return img_stream

@app.route("/sh",methods=['GET','POST'])
def imgshow():
    img_path = './'+str(filename)
    img_stream = return_img_stream(img_path)
    return render_template('detect.html',img_stream=img_stream)

@app.route("/detect",methods=['GET','POST'])
def detect():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath

        #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict(file_path)

    return render_template('detect.html')

@app.route("/document",methods=['GET','POST'])
def document():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict_docu(file_path)
    return render_template('document.html')


@app.route("/face_detect",methods=['GET','POST'])
def face():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict_face(file_path)
    return render_template('face.html')

@app.route("/cat_detect",methods=['GET','POST'])
def cat():
    if request.method=='POST':
        f = request.files['file'] #获取客户端传输的名为file的文件
        global filename
        filename = f.filename
        global filepath #获取当期文件夹
        file_path = os.path.join(os.getcwd(),filename)
        f.save(file_path)
        predict_cat(file_path)
    return render_template('cat.html')

if __name__ == '__main__':
    app.run(host="0.0.0.0",port=5000)
